apply plugin: 'com.android.application'
apply plugin: 'org.jetbrains.kotlin.android'

android {
    compileSdkVersion 34
    buildToolsVersion '34.0.0'
    useLibrary 'org.apache.http.legacy'
    defaultConfig {
        namespace "com.watchrx.watchrxhealth"
        minSdkVersion 26
        targetSdkVersion 34
        versionCode 43
        versionName "v1.0.43"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    signingConfigs {
        release {
            storeFile file(rootProject.file(MY_KEYSTORE_FILE))
            storePassword MY_KEYSTORE_PASSWORD
            keyAlias MY_KEY_ALIAS
            keyPassword MY_KEY_PASSWORD
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE.txt'
    }
    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    configurations {
        all*.exclude group: 'com.google.guava', module: 'listenablefuture'
    }
}

dependencies {

    implementation fileTree(dir: 'libs', include: '*.jar')
    implementation 'androidx.appcompat:appcompat:1.4.1'
    implementation 'commons-codec:commons-codec:1.15'
    implementation 'us.zoom.uitoolkit:uitoolkit:1.11.2-1'

    implementation 'com.github.GoodieBag:Pinview:v1.4'

    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'com.google.android.material:material:1.12.0'
//    implementation 'com.google.android.material:material:1.5.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.2.0'
    implementation 'com.google.code.gson:gson:2.8.6'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'com.squareup.okhttp3:okhttp:3.10.0'
    implementation 'com.google.firebase:firebase-messaging:24.1.1'
    implementation ('com.google.firebase:firebase-iid:21.1.0') {
        transitive = true
    }
    implementation 'com.google.android.gms:play-services-location:21.3.0'
    implementation 'com.google.android.gms:play-services-auth:21.3.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.3'
    implementation 'androidx.recyclerview:recyclerview:1.2.1'
    implementation 'com.google.firebase:firebase-messaging-directboot:23.0.2'

    implementation 'org.apache.httpcomponents:httpmime:4.5.14' 
    implementation 'com.google.android.gms:play-services-maps:18.1.0'
    implementation 'com.google.android.gms:play-services-location:21.0.1'
    implementation 'com.google.android.gms:play-services-auth:21.3.0'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'commons-io:commons-io:2.13.0'

    implementation 'com.google.android.material:material:1.9.0'
    implementation 'com.applandeo:material-calendar-view:1.9.2'

    implementation 'androidx.lifecycle:lifecycle-process:2.4.1'
    implementation 'org.greenrobot:eventbus:3.3.1'
    implementation "com.twilio:audioswitch:1.2.0"
    implementation 'com.twilio:video-android:7.0.3'
    implementation 'androidx.preference:preference:1.2.0'
    implementation "org.jetbrains.kotlin:kotlin-stdlib:1.6.0"
    implementation 'androidx.work:work-runtime:2.7.1'
    implementation 'androidx.work:work-runtime-ktx:2.7.1'
    implementation 'androidx.gridlayout:gridlayout:1.0.0'

    implementation 'com.twilio:voice-android:6.9.0'
    implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0'
    implementation 'androidx.activity:activity:1.8.0'
    def aws_version = "2.16.+"
    implementation ("com.amazonaws:aws-android-sdk-lex:$aws_version@aar") { transitive=true }
    implementation ("com.amazonaws:aws-android-sdk-mobile-client:$aws_version@aar") { transitive=true }

    apply plugin: 'com.google.gms.google-services'
}