<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/activity_medication"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MedicationActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="vertical"
        android:weightSum="3"
        app:layout_constraintBottom_toTopOf="@+id/nav_view"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/titleLayout"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_margin="5dp"
            android:layout_weight="0.4"
            android:orientation="vertical"
            android:weightSum="1"
            app:layout_constraintBottom_toBottomOf="parent">

            <TextView
                android:id="@+id/textView_strength"
                style="@style/Widget.AppCompat.Button.Colored"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_margin="5dp"
                android:layout_weight="1"
                android:fontFamily="@font/lato_black"
                android:textSize="30sp"
                app:cornerRadius="@dimen/d_20_d"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


        </LinearLayout>

        <LinearLayout
            android:id="@+id/LEDs"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_margin="2dp"
            android:layout_weight="1.2"
            android:gravity="center_horizontal"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="@+id/titleLayout">

            <ImageView
                android:id="@+id/image_from_database"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="10dp"
                android:layout_marginTop="@dimen/d_15_d"
                android:scaleType="fitXY"
                android:src="@drawable/deafult_medicine"
                app:layout_constraintTop_toTopOf="parent" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/speechDetails"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_gravity="center_horizontal|center_vertical"
            android:layout_margin="5dp"
            android:layout_weight="1.4"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:weightSum="2"
            app:layout_constraintBottom_toBottomOf="@+id/LEDs">

            <TextView
                android:id="@+id/textView_medicine_name_dosage"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_margin="10dp"
                android:layout_weight="0.7"
                android:fontFamily="@font/lato_light"
                android:gravity="center"
                android:textSize="30sp" />

            <TextView
                android:id="@+id/have_taken"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_margin="10dp"
                android:layout_weight="1.3"
                android:fontFamily="@font/lato_light"
                android:gravity="center_horizontal|center_vertical"
                android:text="@string/have_you_taken_meds"
                android:textSize="25sp"
                android:textStyle="bold" />

            <!-- Vertical buttons layout -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:gravity="center"
                android:orientation="vertical">


                <com.google.android.material.button.MaterialButton
                    android:id="@+id/button_yes"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="@string/yes"
                    android:textColor="@android:color/white"
                    app:backgroundTint="@android:color/holo_green_dark"
                    app:cornerRadius="16dp"
                    app:elevation="4dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/button_no"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="@string/no"
                    android:textColor="@android:color/white"
                    app:backgroundTint="@android:color/holo_red_dark"
                    app:cornerRadius="16dp"
                    app:elevation="4dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/button_remind"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/remind_me_again"
                    android:textColor="@android:color/white"
                    app:backgroundTint="@android:color/holo_orange_dark"
                    app:cornerRadius="16dp"
                    app:elevation="4dp" />

            </LinearLayout>
        </LinearLayout>


    </LinearLayout>

    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/nav_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="0dp"
        android:layout_marginEnd="0dp"
        android:layout_weight="0.3"
        android:background="@color/statusbar"
        android:visibility="gone"
        app:itemIconSize="40dp"
        app:itemIconTint="@drawable/bottom_navigation_selector"
        app:labelVisibilityMode="unlabeled"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:menu="@menu/bottom_nav_menu" />

</androidx.constraintlayout.widget.ConstraintLayout>