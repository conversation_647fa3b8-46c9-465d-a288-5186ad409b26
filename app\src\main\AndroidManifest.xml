<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.watchrx.watchrxhealth">

    <uses-feature
        android:name="android.hardware.camera"
        tools:ignore="ManifestOrder" />
    <uses-feature android:name="android.hardware.camera.autofocus" />
    <uses-feature
        android:glEsVersion="0x00020000"
        android:required="true" />

    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
    <uses-permission android:name="android.permission.BODY_SENSORS" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission
        android:name="android.permission.BLUETOOTH"
        android:maxSdkVersion="30" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_ADMIN"
        android:maxSdkVersion="30" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_SCAN"
        android:usesPermissionFlags="neverForLocation" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />

    <uses-feature
        android:name="android.hardware.bluetooth_le"
        android:required="false" />

    <uses-permission
        android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION"
        tools:node="remove" />

    <application
        android:name=".WatchApp"
        android:allowBackup="true"
        android:appComponentFactory="@string/app_name"
        android:enabled="true"
        android:extractNativeLibs="true"
        android:fullBackupContent="false"
        android:icon="@mipmap/watchrx_app_icon_round"
        android:label="@string/app_name"
        android:persistent="true"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        android:theme="@style/Theme.WatchRx"
        android:usesCleartextTraffic="true"
        android:windowSoftInputMode="adjustResize"
        tools:ignore="GoogleAppIndexingWarning"
        tools:overrideLibrary="us.zoom.videomeetings"
        tools:replace="android:appComponentFactory">
        <activity
            android:name=".LatestAlertsActivity"
            android:exported="false" />
        <activity
            android:name=".ZoomVideoCallScreen"
            android:exported="false" />
        <activity
            android:name=".auth.EnterOTPActivity"
            android:exported="false" />
        <activity
            android:name=".auth.EnterPasswordActivity"
            android:exported="false" />

        <activity
            android:name=".auth.ResendOTPScreen"
            android:exported="false" />
        <activity
            android:name=".auth.LoginScreen"
            android:configChanges="orientation|screenSize|keyboardHidden|uiMode"
            android:exported="true"
            android:launchMode="standard"
            android:singleUser="true"
            tools:targetApi="33">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".LoginActivity"
            tools:ignore="NewApi" />
        <activity
            android:name=".ChatActivity"
            android:exported="false" />
        <activity
            android:name=".VitalsGraphActivity"
            android:exported="false" />
        <activity
            android:name=".MyTaskCalendar"
            android:exported="false" />
        <activity
            android:name=".WebViewActivity"
            android:exported="false" />
        <activity
            android:name=".ReminderDetailsActivity"
            android:exported="false" />
        <activity
            android:name=".VitalDashboard"
            android:exported="false" />
        <activity
            android:name=".InteractiveVoiceActivity"
            android:exported="false" />
        <activity
            android:name=".PatientDiaryActivity"
            android:screenOrientation="fullSensor" />
        <activity android:name=".SleepMonitorActivity" />
        <activity android:name=".SplashActivity" />
        <activity
            android:name=".VitalDetailsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ViewAllTextMessageActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".MedicationDetailsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".GPSActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".TextMessageActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name=".PhoneCallsActivity"
            android:screenOrientation="portrait" />
        <activity android:name=".WifiConfig" />
        <activity
            android:name=".CustomAlertActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".MainActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ReminderActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".AddMedication"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".MedicationActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".NurseOnTheWayActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".VisitVerificationActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".BatteryActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".AlertsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".HeartRateActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".PedoMeterActivity"
            android:screenOrientation="portrait" /> <!-- <activity -->
        <!-- android:name=".ble.NewVitalsActivity" -->
        <!-- android:screenOrientation="portrait" /> -->
        <activity
            android:name=".ScheduleTextMessageActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".twilio.VideoActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".twilio.SettingsActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity android:name=".ble.NewVitalsActivity" />
        <activity
            android:name=".voip.IncomingCallActivity"
            android:launchMode="singleTask" />

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />
        <meta-data
            android:name="firebase_messaging_auto_init_enabled"
            android:value="false" />
        <meta-data
            android:name="firebase_analytics_collection_enabled"
            android:value="false" />
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="AIzaSyDhGEgJCRCYBnheNQ9TVFY3h7Byn0oq6R4" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="@string/default_notification_channel_id" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.watchrx.watchrxhealth.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/filepaths" />
        </provider>

        <receiver
            android:name=".receivers.CheckQueueReceiver"
            android:enabled="true"
            android:exported="true" />
        <receiver
            android:name=".receivers.ReminderReceiver"
            android:enabled="true"
            android:exported="true" />
        <receiver
            android:name=".receivers.CustomAlertReceiver"
            android:enabled="true"
            android:exported="true" />
        <receiver
            android:name=".syncup.MedicationScheduleSetupReceiver"
            android:enabled="true"
            android:exported="true" />
        <receiver
            android:name=".syncup.SyncupReceiver"
            android:enabled="true"
            android:exported="true" />
        <receiver
            android:name=".receivers.RebootComplete"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.RECEIVE_BOOT_COMPLETED">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
                <action android:name="android.intent.action.ACTION_SHUTDOWN" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>
        <receiver
            android:name=".receivers.TimeZoneChangedReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name=".receivers.NewPackageInstalled"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.PACKAGE_ADDED" />
                <action android:name="android.intent.action.PACKAGE_INSTALL" />
                <action android:name="android.intent.action.PACKAGE_REMOVED" />
                <action android:name="android.intent.action.PACKAGE_REPLACED" />

                <category android:name="android.intent.category.DEFAULT" />

                <data android:scheme="package" />
            </intent-filter>
        </receiver>
        <receiver
            android:name=".receivers.InternetStatusChangeReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
                <!-- <action android:name="android.net.wifi.WIFI_STATE_CHANGED" /> -->
            </intent-filter>
        </receiver>
        <receiver
            android:name=".receivers.HrtBroadcastReceiver"
            android:enabled="true"
            android:exported="true" />
        <receiver
            android:name=".receivers.NetworkHeartBeat"
            android:enabled="true"
            android:exported="true" />
        <receiver
            android:name=".receivers.ScheduleMessageReceiver"
            android:enabled="true"
            android:exported="true" />
        <receiver
            android:name=".receivers.SendPedoMeterReceiver"
            android:enabled="true"
            android:exported="true" />
        <receiver
            android:name=".receivers.MidnightPedoMeterResetReceiver"
            android:enabled="true"
            android:exported="true" />
        <receiver
            android:name=".receivers.VitalReminderReceiver"
            android:enabled="true"
            android:exported="true" />
        <receiver
            android:name=".receivers.MidnightLogFileUploadReceiver"
            android:enabled="true"
            android:exported="true" />
        <receiver
            android:name=".receivers.BleBroadCastReceiver"
            android:enabled="true"
            android:exported="true" />

        <service
            android:name=".gcm.GCMPushReceiverService"
            android:directBootAware="true"
            android:exported="true"
            android:permission="true"
            tools:ignore="KnownPermissionError">
            <intent-filter>
                <action android:name="com.google.firebase.INSTANCE_ID_EVENT" />
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
        <service
            android:name=".gcm.GCMRegistrationIntentService"
            android:exported="false" />
        <service
            android:name=".gps.GeoFenceIntentServices"
            android:exported="false" />
        <service
            android:name=".pedometer.SensorListener"
            android:exported="false" />
        <service
            android:name=".WatchRxForegroundService"
            android:exported="false"
            android:foregroundServiceType="mediaPlayback"
            android:permission="android.permission.FOREGROUND_SERVICE" />
        <service
            android:name="com.zipow.videobox.share.ScreenShareServiceForSDK"
            android:exported="false"
            android:foregroundServiceType="microphone|connectedDevice"
            android:label="Zoom"
            tools:ignore="ForegroundServicePermission"
            tools:replace="android:foregroundServiceType" />

        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />
    </application>

</manifest>